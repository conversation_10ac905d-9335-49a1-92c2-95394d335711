{"data": {"gameCustomTypeLists": [], "gameLists": [{"gameID": "57", "gameNameEn": "Dragon Hatch", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/57.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "103", "gameNameEn": "Crypto Gold", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/103.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "42", "gameNameEn": "Ganesha Gold", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/42.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "75", "gameNameEn": "G<PERSON><PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/75.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "108", "gameNameEn": "Buffalo Win", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/108.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "127", "gameNameEn": "Speed Winner", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/127.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "26", "gameNameEn": "Tree of Fortune", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/26.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "101", "gameNameEn": "Rise of Apollo", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/101.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "39", "gameNameEn": "<PERSON>gy Gold", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/39.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "135", "gameNameEn": "Wild Bounty Showdown", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/135.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1312883", "gameNameEn": "Prosperity Fortune Tree", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1312883.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "100", "gameNameEn": "<PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/100.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "98", "gameNameEn": "Fortune Ox", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/98.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "89", "gameNameEn": "<PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/89.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "60", "gameNameEn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/60.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "73", "gameNameEn": "Egypt's Book of Mystery", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/73.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "87", "gameNameEn": "Treasures of Aztec", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/87.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "117", "gameNameEn": "Cocktail Nights", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/117.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "6", "gameNameEn": "Medusa 2: The Quest of Perseus", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/6.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "48", "gameNameEn": "Double Fortune", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/48.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "128", "gameNameEn": "Legend of <PERSON><PERSON><PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/128.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "74", "gameNameEn": "Mahjong Ways 2", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/74.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "106", "gameNameEn": "Ways of the Qilin", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/106.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "126", "gameNameEn": "Fortune Tiger", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/126.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "104", "gameNameEn": "Wild Bandito", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/104.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "65", "gameNameEn": "<PERSON><PERSON><PERSON> Ways", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/65.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "132", "gameNameEn": "Wild Coaster", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/132.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "121", "gameNameEn": "Destiny of Sun & Moon", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/121.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "63", "gameNameEn": "Dragon Tiger Luck", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/63.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "54", "gameNameEn": "Captain's <PERSON><PERSON>y", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/54.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "71", "gameNameEn": "<PERSON><PERSON><PERSON><PERSON>s", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/71.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "124", "gameNameEn": "Battleground Royale", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/124.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "70", "gameNameEn": "<PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/70.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "130", "gameNameEn": "<PERSON> Piggy", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/130.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "115", "gameNameEn": "Supermarket Spree", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/115.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "79", "gameNameEn": "Dreams of Macau", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/79.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "40", "gameNameEn": "Jungle Delight", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/40.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "85", "gameNameEn": "<PERSON><PERSON>'s 3 Wishes", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/85.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "88", "gameNameEn": "Jewels of Prosperity", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/88.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "68", "gameNameEn": "Fortune Mouse", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/68.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "95", "gameNameEn": "Majestic Treasures", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/95.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "84", "gameNameEn": "Queen of Bounty", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/84.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "113", "gameNameEn": "Raider Jane's Crypt of Fortune", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/113.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "82", "gameNameEn": "Phoenix Rises", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/82.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "123", "gameNameEn": "Rooster Rumble", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/123.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "91", "gameNameEn": "Guardians of Ice & Fire", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/91.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "125", "gameNameEn": "Butterfly Blossom", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/125.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "90", "gameNameEn": "Secrets of Cleopatra", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/90.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "24", "gameNameEn": "Win Win Won", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/24.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "92", "gameNameEn": "Thai River Wonders", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/92.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "83", "gameNameEn": "Wild Fireworks", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/83.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1543462", "gameNameEn": "Fortune Rabbit", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1543462.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "28", "gameNameEn": "Hotpot", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/28.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "120", "gameNameEn": "The Queen's Banquet", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/120.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "41", "gameNameEn": "Symbols Of Egypt", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/41.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "86", "gameNameEn": "Galactic Gems", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/86.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "107", "gameNameEn": "Legendary Monkey King", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/107.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "53", "gameNameEn": "The Great Icescape", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/53.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "69", "gameNameEn": "Bikini Paradise", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/69.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "122", "gameNameEn": "Garuda Gems", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/122.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "94", "gameNameEn": "Bali Vacation", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/94.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "97", "gameNameEn": "<PERSON>'s Winter", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/97.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "3", "gameNameEn": "Fortune Gods", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/3.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "38", "gameNameEn": "Gem Saviour Sword", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/38.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "29", "gameNameEn": "Dragon Legend", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/29.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "34", "gameNameEn": "Legend of <PERSON><PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/34.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "33", "gameNameEn": "Hip Hop Panda", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/33.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "80", "gameNameEn": "Circus Delight", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/80.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "119", "gameNameEn": "Spirited Wonders", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/119.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "93", "gameNameEn": "Opera Dynasty", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/93.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "129", "gameNameEn": "Win Win Fish Prawn Crab", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/129.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "102", "gameNameEn": "Mermaid Riches", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/102.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "112", "gameNameEn": "Oriental Prosperity", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/112.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "64", "gameNameEn": "Muay Thai Champion", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/64.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "118", "gameNameEn": "Mask Carnival", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/118.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "58", "gameNameEn": "Vampire's Charm", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/58.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "31", "gameNameEn": "Baccarat Deluxe", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/31.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "105", "gameNameEn": "Heist Stakes", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/105.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "7", "gameNameEn": "Medusa 1: The Curse of Athena", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/7.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "59", "gameNameEn": "Ninja vs Samurai", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/59.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "50", "gameNameEn": "Journey to the Wealth", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/50.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "110", "gameNameEn": "Jurassic Kingdom", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/110.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "62", "gameNameEn": "Gem Saviour Conquest", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/62.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "20", "gameNameEn": "<PERSON><PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/20.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "114", "gameNameEn": "<PERSON><PERSON><PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/114.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "25", "gameNameEn": "<PERSON><PERSON><PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/25.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "37", "gameNameEn": "Santa's Gift Rush", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/37.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "35", "gameNameEn": "Mr. <PERSON><PERSON>-Win", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/35.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1", "gameNameEn": "Honey Trap of Diao Chan", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "36", "gameNameEn": "Prosperity Lion", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/36.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "18", "gameNameEn": "<PERSON> vs <PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/18.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "44", "gameNameEn": "Emperor's Favour", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/44.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "61", "gameNameEn": "Flirting Scholar", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/61.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "67", "gameNameEn": "Shaolin Soccer", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/67.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "2", "gameNameEn": "Gem Saviour", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/2.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1473388", "gameNameEn": "Cruise Royale", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1473388.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1340277", "gameNameEn": "Asgardian Rising", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1340277.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1381200", "gameNameEn": "Hawaiian Tiki", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1381200.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1368367", "gameNameEn": "Alchemy Gold", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1368367.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1594259", "gameNameEn": "Safari Wilds", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1594259.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1402846", "gameNameEn": "Midas Fortune", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1402846.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1568554", "gameNameEn": "Wild Heist Cashout", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1568554.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1529867", "gameNameEn": "<PERSON> <PERSON><PERSON><PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1529867.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1555350", "gameNameEn": "Forge of Wealth", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1555350.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1418544", "gameNameEn": "<PERSON><PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1418544.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1432733", "gameNameEn": "Mystical Spirits", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1432733.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1489936", "gameNameEn": "Ultimate Striker", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1489936.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1397455", "gameNameEn": "Fruity Candy", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1397455.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1448762", "gameNameEn": "Songkran Splash", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1448762.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1601012", "gameNameEn": "Lucky Clover Lady", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1601012.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1513328", "gameNameEn": "Super Golf Drive", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1513328.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1338274", "gameNameEn": "Totem Wonders", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1338274.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1420892", "gameNameEn": "Rave <PERSON>", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1420892.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1372643", "gameNameEn": "Diner Delights", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1372643.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1815268", "gameNameEn": "Oishi Delights", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1815268.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1786529", "gameNameEn": "Rio Fantasia", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1786529.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1666445", "gameNameEn": "Chocolate Deluxe", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1666445.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}, {"gameID": "1702123", "gameNameEn": "<PERSON><PERSON><PERSON>'s Revenge", "img": "https://ossimg.51game-game.com/51game/gamelogo/PG/1702123.png", "vendorId": 5, "vendorCode": "PG", "imgUrl2": null, "customGameType": 0}]}, "code": 0, "msg": "Succeed", "msgCode": 0, "serviceNowTime": "2025-01-28 20:58:06"}