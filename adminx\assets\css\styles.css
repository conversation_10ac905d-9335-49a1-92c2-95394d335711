body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-image: url("../images/background.jpg"); /* Replace with your background image -->*/
  background-size: cover;
  background-position: center;
  height: 100vh;
}

.container {
  display: flex; /* Use flexbox for responsive layout */
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;  /* Adapt to full width on mobile */
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  text-align: center;
  border-radius: 5px;
  margin: auto;
  min-height: 100vh;
}

img {
  width: 100px;
  height: auto;
  margin-bottom: 20px;
}

h1 {
  font-size: 24px;
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 5px;
}

input[type="text"],
input[type="password"] {
  width: 80%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 3px;
  margin-bottom: 15px;
}

button {
  background-color: #4CAF50;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

button:hover {
  opacity: 0.8;
}

.forgot-password {
  color: #ccc;
  text-decoration: none;
  font-size: 12px;
  display: block;
  margin-top: 10px;
}
