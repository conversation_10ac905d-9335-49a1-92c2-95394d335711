{"version": 3, "sources": ["jquery.barrating.js"], "names": ["factory", "define", "amd", "module", "exports", "require", "j<PERSON><PERSON><PERSON>", "$", "BarRating", "self", "this", "wrapElement", "classes", "options", "theme", "push", "$elem", "wrap", "class", "join", "unwrapElement", "unwrap", "findOption", "value", "isNumeric", "Math", "floor", "getInitialOption", "initialRating", "getEmptyOption", "$emptyOpt", "find", "emptyValue", "length", "allowEmpty", "prependTo", "getData", "key", "data", "setData", "saveDataOnElement", "$opt", "val", "text", "emptyText", "userOptions", "ratingValue", "ratingText", "originalRatingValue", "originalRatingText", "emptyRatingValue", "emptyRatingText", "readOnly", "readonly", "ratingMade", "removeDataOnElement", "removeData", "buildWidget", "$w", "each", "html", "$a", "href", "data-rating-value", "data-rating-text", "append", "showSelectedRating", "reverse", "addClass", "nextAllorPreviousAll", "setSelectFieldValue", "prop", "change", "resetSelectField", "defaultSelected", "parent", "fraction", "round", "resetStyle", "$widget", "removeClass", "index", "match", "applyStyle", "$all", "$fractional", "baseValue", "f", "isDeselectable", "$element", "deselectable", "attr", "attachClickHandler", "$elements", "on", "event", "preventDefault", "onSelect", "call", "attachMouseEnterHandler", "attachMouseLeaveHandler", "fastClicks", "stopPropagation", "click", "disableClicks", "attachHandlers", "hoverState", "detachHandlers", "off", "setupHandlers", "show", "insertAfter", "hide", "state", "toggleClass", "set", "silent", "clear", "onClear", "destroy", "remove", "onDestroy", "prototype", "init", "elem", "extend", "fn", "barrating", "defaults", "method", "plugin", "is", "error", "hasOwnProperty", "next", "showValues"], "mappings": "CAUC,SAAUA,GACe,kBAAXC,SAAyBA,OAAOC,IAEvCD,QAAQ,UAAWD,GACM,gBAAXG,SAAuBA,OAAOC,QAE5CD,OAAOC,QAAUJ,EAAQK,QAAQ,WAGjCL,EAAQM,SAEd,SAAUC,GAER,GAAIC,GAAY,WAEZ,QAASA,KACL,GAAIC,GAAOC,KAGPC,EAAc,WACd,GAAIC,IAAW,aAEY,MAAvBH,EAAKI,QAAQC,OACbF,EAAQG,KAAK,YAAcN,EAAKI,QAAQC,OAG5CL,EAAKO,MAAMC,KAAKV,EAAE,WACdW,QAASN,EAAQO,KAAK,SAK1BC,EAAgB,WAChBX,EAAKO,MAAMK,UAIXC,EAAa,SAASC,GAKtB,MAJIhB,GAAEiB,UAAUD,KACZA,EAAQE,KAAKC,MAAMH,IAGhBhB,EAAE,iBAAmBgB,EAAS,KAAMd,EAAKO,QAIhDW,EAAmB,WACnB,GAAIC,GAAgBnB,EAAKI,QAAQe,aAEjC,OAAKA,GAIEN,EAAWM,GAHPrB,EAAE,kBAAmBE,EAAKO,QAOrCa,EAAiB,WACjB,GAAIC,GAAYrB,EAAKO,MAAMe,KAAK,iBAAmBtB,EAAKI,QAAQmB,WAAa,KAE7E,QAAKF,EAAUG,QAAUxB,EAAKI,QAAQqB,YAClCJ,EAAYvB,EAAE,cAAgBgB,MAASd,EAAKI,QAAQmB,aAE7CF,EAAUK,UAAU1B,EAAKO,QAG7Bc,GAIPM,EAAU,SAASC,GACnB,GAAIC,GAAO7B,EAAKO,MAAMsB,KAAK,YAE3B,OAAmB,mBAARD,GACAC,EAAKD,GAGTC,GAIPC,EAAU,SAASF,EAAKd,GACV,OAAVA,GAAmC,gBAAVA,GACzBd,EAAKO,MAAMsB,KAAK,YAAaf,GAE7Bd,EAAKO,MAAMsB,KAAK,aAAaD,GAAOd,GAKxCiB,EAAoB,WACpB,GAAIC,GAAOd,IACPG,EAAYD,IAEZN,EAAQkB,EAAKC,MACbC,EAAOF,EAAKH,KAAK,QAAUG,EAAKH,KAAK,QAAUG,EAAKE,OAGpDT,EAA0C,OAA5BzB,EAAKI,QAAQqB,WAC3BzB,EAAKI,QAAQqB,aACXJ,EAAUG,OAEZD,EAAcF,EAAgB,OAAIA,EAAUY,MAAQ,KACpDE,EAAad,EAAgB,OAAIA,EAAUa,OAAS,IAExDJ,GAAQ,MACJM,YAAapC,EAAKI,QAGlBiC,YAAavB,EACbwB,WAAYJ,EAGZK,oBAAqBzB,EACrB0B,mBAAoBN,EAGpBT,WAAYA,EAGZgB,iBAAkBlB,EAClBmB,gBAAiBP,EAGjBQ,SAAU3C,EAAKI,QAAQwC,SAGvBC,YAAY,KAKhBC,EAAsB,WACtB9C,EAAKO,MAAMwC,WAAW,cAItBT,EAAa,WACb,MAAOX,GAAQ,eAIfU,EAAc,WACd,MAAOV,GAAQ,gBAIfqB,EAAc,WACd,GAAIC,GAAKnD,EAAE,WAAaW,QAAS,aAwCjC,OArCAT,GAAKO,MAAMe,KAAK,UAAU4B,KAAK,WAC3B,GAAIjB,GAAKC,EAAMiB,EAAMC,CAErBnB,GAAMnC,EAAEG,MAAMgC,MAGVA,IAAQN,EAAQ,sBAChBO,EAAOpC,EAAEG,MAAMiC,OACfiB,EAAOrD,EAAEG,MAAM4B,KAAK,QAChBsB,IAAQjB,EAAOiB,GAEnBC,EAAKtD,EAAE,SACHuD,KAAQ,IACRC,oBAAqBrB,EACrBsB,mBAAoBrB,EACpBiB,KAASnD,EAAKI,QAAkB,WAAI8B,EAAO,KAG/Ce,EAAGO,OAAOJ,MAMdpD,EAAKI,QAAQqD,oBACbR,EAAGO,OAAO1D,EAAE,WAAaoC,KAAQ,GAAIzB,QAAS,uBAI9CT,EAAKI,QAAQsD,SACbT,EAAGU,SAAS,cAGZ3D,EAAKI,QAAQwC,UACbK,EAAGU,SAAS,eAGTV,GAIPW,EAAuB,WACvB,MAAIjC,GAAQ,eAAe+B,QAChB,UAEA,WAKXG,EAAsB,SAAS/C,GAE/BD,EAAWC,GAAOgD,KAAK,YAAY,GAEnC9D,EAAKO,MAAMwD,UAIXC,EAAmB,WACnBlE,EAAE,SAAUE,EAAKO,OAAOuD,KAAK,WAAY,WACrC,MAAO7D,MAAKgE,kBAGhBjE,EAAKO,MAAMwD,UAIXN,EAAqB,SAASvB,GAE9BA,EAAOA,EAAOA,EAAOI,IAGjBJ,GAAQP,EAAQ,qBAChBO,EAAO,IAIPlC,EAAKI,QAAQqD,oBACbzD,EAAKO,MAAM2D,SAAS5C,KAAK,sBAAsBY,KAAKA,IAKxDiC,EAAW,SAASrD,GACpB,MAAOE,MAAKoD,MAAQpD,KAAKC,MAAc,GAARH,GAAc,GAAM,EAAK,MAIxDuD,EAAa,WAEbrE,EAAKsE,QAAQhD,KAAK,KAAKiD,YAAY,SAASC,EAAOrE,GAC/C,OAAQA,EAAQsE,MAAM,sBAAwB/D,KAAK,QAKvDgE,EAAa,WACb,GAIIC,GAAMC,EAJNxB,EAAKpD,EAAKsE,QAAQhD,KAAK,wBAA0Be,IAAgB,MACjElB,EAAgBQ,EAAQ,eAAeR,cACvC0D,EAAY/E,EAAEiB,UAAUsB,KAAiBA,IAAgB,EACzDyC,EAAIX,EAAShD,EASjB,IANAkD,IAGAjB,EAAGO,SAAS,0BAA0BC,OACjCD,SAAS,gBAEThC,EAAQ,eAAiB7B,EAAEiB,UAAUI,GAAgB,CACtD,GAAsB0D,GAAjB1D,IAAgC2D,EACjC,MAGJH,GAAO3E,EAAKsE,QAAQhD,KAAK,KAEzBsD,EAAexB,EAAS,OACpBA,EAAIzB,EAAQ,eAAsB,QAAI,OAAS,UAC/CgD,EAAMhD,EAAQ,eAAsB,QAAI,OAAS,WAErDiD,EAAYjB,SAAS,iBACrBiB,EAAYjB,SAAS,iBAAmBmB,KAK5CC,EAAiB,SAASC,GAC1B,MAAKrD,GAAQ,eAAkBA,EAAQ,eAAesD,aAI9C5C,KAAiB2C,EAASE,KAAK,sBAH5B,GAOXC,EAAqB,SAASC,GAC9BA,EAAUC,GAAG,kBAAmB,SAASC,GACrC,GAEIxE,GACAoB,EAHAkB,EAAKtD,EAAEG,MACPG,EAAUuB,EAAQ,cAiCtB,OA7BA2D,GAAMC,iBAENzE,EAAQsC,EAAG8B,KAAK,qBAChBhD,EAAOkB,EAAG8B,KAAK,oBAGXH,EAAe3B,KACftC,EAAQa,EAAQ,oBAChBO,EAAOP,EAAQ,oBAInBG,EAAQ,cAAehB,GACvBgB,EAAQ,aAAcI,GACtBJ,EAAQ,cAAc,GAEtB+B,EAAoB/C,GACpB2C,EAAmBvB,GAEnBwC,IAGAtE,EAAQoF,SAASC,KACbzF,EACAqC,IACAC,IACAgD,IAGG,KAKXI,EAA0B,SAASN,GACnCA,EAAUC,GAAG,uBAAwB,WACjC,GAAIjC,GAAKtD,EAAEG,KAEXoE,KAEAjB,EAAGO,SAAS,aAAaC,OACpBD,SAAS,aAEdF,EAAmBL,EAAG8B,KAAK,wBAK/BS,EAA0B,SAASP,GACnCpF,EAAKsE,QAAQe,GAAG,sCAAuC,WACnD5B,IACAiB,OAOJkB,EAAa,SAASR,GACtBA,EAAUC,GAAG,uBAAwB,SAASC,GAC1CA,EAAMC,iBACND,EAAMO,kBAEN/F,EAAEG,MAAM6F,WAKZC,EAAgB,SAASX,GACzBA,EAAUC,GAAG,kBAAmB,SAASC,GACrCA,EAAMC,oBAIVS,EAAiB,SAASZ,GAE1BD,EAAmBC,GAEfpF,EAAKI,QAAQ6F,aAEbP,EAAwBN,GAGxBO,EAAwBP,KAI5Bc,EAAiB,SAASd,GAE1BA,EAAUe,IAAI,eAGdC,EAAgB,SAASxD,GACzB,GAAIwC,GAAYpF,EAAKsE,QAAQhD,KAAK,IAE9BsE,IACAA,EAAWR,GAGXxC,GACAsD,EAAed,GACfW,EAAcX,IAEdY,EAAeZ,GAIvBnF,MAAKoG,KAAO,WAEJ1E,MAGJzB,IAGA6B,IAGA/B,EAAKsE,QAAUtB,IACfhD,EAAKsE,QAAQgC,YAAYtG,EAAKO,OAE9BmE,IAEAjB,IAEA2C,EAAcpG,EAAKI,QAAQwC,UAG3B5C,EAAKO,MAAMgG,SAGftG,KAAK2C,SAAW,SAAS4D,GACA,iBAAVA,IAAuB7E,EAAQ,aAAe6E,IAEzDJ,EAAcI,GACd1E,EAAQ,WAAY0E,GACpBxG,EAAKsE,QAAQmC,YAAY,iBAG7BxG,KAAKyG,IAAM,SAAS5F,GAChB,GAAIV,GAAUuB,EAAQ,cAE0C,KAA5D3B,EAAKO,MAAMe,KAAK,iBAAmBR,EAAQ,MAAMU,SAGrDM,EAAQ,cAAehB,GACvBgB,EAAQ,aAAc9B,EAAKO,MAAMe,KAAK,iBAAmBR,EAAQ,MAAMoB,QACvEJ,EAAQ,cAAc,GAEtB+B,EAAoBxB,KACpBoB,EAAmBnB,KAEnBoC,IAGKtE,EAAQuG,QACTvG,EAAQoF,SAASC,KACbxF,KACAoC,IACAC,OAKZrC,KAAK2G,MAAQ,WACT,GAAIxG,GAAUuB,EAAQ,cAGtBG,GAAQ,cAAeH,EAAQ,wBAC/BG,EAAQ,aAAcH,EAAQ,uBAC9BG,EAAQ,cAAc,GAEtBkC,IACAP,EAAmBnB,KAEnBoC,IAGAtE,EAAQyG,QAAQpB,KACZxF,KACAoC,IACAC,MAIRrC,KAAK6G,QAAU,WACX,GAAIhG,GAAQuB,IACRH,EAAOI,IACPlC,EAAUuB,EAAQ,cAGtBuE,GAAelG,EAAKsE,QAAQhD,KAAK,MAGjCtB,EAAKsE,QAAQyC,SAGbjE,IAGAnC,IAGAX,EAAKO,MAAM8F,OAGXjG,EAAQ4G,UAAUvB,KACdxF,KACAa,EACAoB,IAYZ,MAPAnC,GAAUkH,UAAUC,KAAO,SAAU9G,EAAS+G,GAI1C,MAHAlH,MAAKM,MAAQT,EAAEqH,GACflH,KAAKG,QAAUN,EAAEsH,UAAWtH,EAAEuH,GAAGC,UAAUC,SAAUnH,GAE9CH,KAAKG,SAGTL,IAGXD,GAAEuH,GAAGC,UAAY,SAAUE,EAAQpH,GAC/B,MAAOH,MAAKiD,KAAK,WACb,GAAIuE,GAAS,GAAI1H,EAQjB,IALKD,EAAEG,MAAMyH,GAAG,WACZ5H,EAAE6H,MAAM,qDAIRF,EAAOG,eAAeJ,GAAS,CAE/B,GADAC,EAAOP,KAAK9G,EAASH,MACN,SAAXuH,EACA,MAAOC,GAAOpB,KAAKjG,EAGnB,IAAIqH,EAAOlH,MAAMsB,KAAK,aAElB,MADA4F,GAAOnD,QAAUxE,EAAEG,MAAM4H,KAAK,cACvBJ,EAAOD,GAAQpH,OAK3B,CAAA,GAAsB,gBAAXoH,KAAwBA,EAGtC,MAFApH,GAAUoH,EACVC,EAAOP,KAAK9G,EAASH,MACdwH,EAAOpB,MAGdvG,GAAE6H,MAAM,UAAYH,EAAS,2CAKzC1H,EAAEuH,GAAGC,UAAUC,UACXlH,MAAM,GACNc,cAAc,KACdM,WAAW,KACXF,WAAW,GACXuG,YAAW,EACXrE,oBAAmB,EACnBwB,cAAa,EACbvB,SAAQ,EACRd,UAAS,EACTgD,YAAW,EACXK,YAAW,EACXU,QAAO,EACPnB,SAAS,SAAU1E,EAAOoB,EAAMoD,KAEhCuB,QAAQ,SAAU/F,EAAOoB,KAEzB8E,UAAU,SAAUlG,EAAOoB,MAI/BpC,EAAEuH,GAAGC,UAAUvH,UAAYA", "file": "jquery.barrating.min.js", "sourcesContent": ["/**\n * jQuery Bar Rating Plugin v1.2.2\n *\n * http://github.com/antennaio/jquery-bar-rating\n *\n * Copyright (c) 2012-2016 <PERSON><PERSON><PERSON>\n *\n * This plugin is available under the MIT license.\n * http://www.opensource.org/licenses/mit-license.php\n */\n(function (factory) {\n    if (typeof define === 'function' && define.amd) {\n        // AMD\n        define(['jquery'], factory);\n    } else if (typeof module === 'object' && module.exports) {\n        // Node/CommonJS\n        module.exports = factory(require('jquery'));\n    } else {\n        // browser globals\n        factory(jQuery);\n    }\n}(function ($) {\n\n    var BarRating = (function() {\n\n        function BarRating() {\n            var self = this;\n\n            // wrap element in a wrapper div\n            var wrapElement = function() {\n                var classes = ['br-wrapper'];\n\n                if (self.options.theme !== '') {\n                    classes.push('br-theme-' + self.options.theme);\n                }\n\n                self.$elem.wrap($('<div />', {\n                    'class': classes.join(' ')\n                }));\n            };\n\n            // unwrap element\n            var unwrapElement = function() {\n                self.$elem.unwrap();\n            };\n\n            // find option by value\n            var findOption = function(value) {\n                if ($.isNumeric(value)) {\n                    value = Math.floor(value);\n                }\n\n                return $('option[value=\"' + value  + '\"]', self.$elem);\n            };\n\n            // get initial option\n            var getInitialOption = function() {\n                var initialRating = self.options.initialRating;\n\n                if (!initialRating) {\n                    return $('option:selected', self.$elem);\n                }\n\n                return findOption(initialRating);\n            };\n\n            // get empty option\n            var getEmptyOption = function() {\n                var $emptyOpt = self.$elem.find('option[value=\"' + self.options.emptyValue + '\"]');\n\n                if (!$emptyOpt.length && self.options.allowEmpty) {\n                    $emptyOpt = $('<option />', { 'value': self.options.emptyValue });\n\n                    return $emptyOpt.prependTo(self.$elem);\n                }\n\n                return $emptyOpt;\n            };\n\n            // get data\n            var getData = function(key) {\n                var data = self.$elem.data('barrating');\n\n                if (typeof key !== 'undefined') {\n                    return data[key];\n                }\n\n                return data;\n            };\n\n            // set data\n            var setData = function(key, value) {\n                if (value !== null && typeof value === 'object') {\n                    self.$elem.data('barrating', value);\n                } else {\n                    self.$elem.data('barrating')[key] = value;\n                }\n            };\n\n            // save data on element\n            var saveDataOnElement = function() {\n                var $opt = getInitialOption();\n                var $emptyOpt = getEmptyOption();\n\n                var value = $opt.val();\n                var text = $opt.data('html') ? $opt.data('html') : $opt.text();\n\n                // if the allowEmpty option is not set let's check if empty option exists in the select field\n                var allowEmpty = (self.options.allowEmpty !== null) ?\n                    self.options.allowEmpty :\n                    !!$emptyOpt.length;\n\n                var emptyValue = ($emptyOpt.length) ? $emptyOpt.val() : null;\n                var emptyText = ($emptyOpt.length) ? $emptyOpt.text() : null;\n\n                setData(null, {\n                    userOptions: self.options,\n\n                    // initial rating based on the OPTION value\n                    ratingValue: value,\n                    ratingText: text,\n\n                    // rating will be restored by calling clear method\n                    originalRatingValue: value,\n                    originalRatingText: text,\n\n                    // allow empty ratings?\n                    allowEmpty: allowEmpty,\n\n                    // rating value and text of the empty OPTION\n                    emptyRatingValue: emptyValue,\n                    emptyRatingText: emptyText,\n\n                    // read-only state\n                    readOnly: self.options.readonly,\n\n                    // did the user already select a rating?\n                    ratingMade: false\n                });\n            };\n\n            // remove data on element\n            var removeDataOnElement = function() {\n                self.$elem.removeData('barrating');\n            };\n\n            // return current rating text\n            var ratingText = function() {\n                return getData('ratingText');\n            };\n\n            // return current rating value\n            var ratingValue = function() {\n                return getData('ratingValue');\n            };\n\n            // build widget and return jQuery element\n            var buildWidget = function() {\n                var $w = $('<div />', { 'class': 'br-widget' });\n\n                // create A elements that will replace OPTIONs\n                self.$elem.find('option').each(function() {\n                    var val, text, html, $a;\n\n                    val = $(this).val();\n\n                    // create ratings - but only if val is not defined as empty\n                    if (val !== getData('emptyRatingValue')) {\n                        text = $(this).text();\n                        html = $(this).data('html');\n                        if (html) { text = html; }\n\n                        $a = $('<a />', {\n                            'href': '#',\n                            'data-rating-value': val,\n                            'data-rating-text': text,\n                            'html': (self.options.showValues) ? text : ''\n                        });\n\n                        $w.append($a);\n                    }\n\n                });\n\n                // append .br-current-rating div to the widget\n                if (self.options.showSelectedRating) {\n                    $w.append($('<div />', { 'text': '', 'class': 'br-current-rating' }));\n                }\n\n                // additional classes for the widget\n                if (self.options.reverse) {\n                    $w.addClass('br-reverse');\n                }\n\n                if (self.options.readonly) {\n                    $w.addClass('br-readonly');\n                }\n\n                return $w;\n            };\n\n            // return a jQuery function name depending on the 'reverse' setting\n            var nextAllorPreviousAll = function() {\n                if (getData('userOptions').reverse) {\n                    return 'nextAll';\n                } else {\n                    return 'prevAll';\n                }\n            };\n\n            // set the value of the select field\n            var setSelectFieldValue = function(value) {\n                // change selected option\n                findOption(value).prop('selected', true);\n\n                self.$elem.change();\n            };\n\n            // reset select field\n            var resetSelectField = function() {\n                $('option', self.$elem).prop('selected', function() {\n                    return this.defaultSelected;\n                });\n\n                self.$elem.change();\n            };\n\n            // display the currently selected rating\n            var showSelectedRating = function(text) {\n                // text undefined?\n                text = text ? text : ratingText();\n\n                // special case when the selected rating is defined as empty\n                if (text == getData('emptyRatingText')) {\n                    text = '';\n                }\n\n                // update .br-current-rating div\n                if (self.options.showSelectedRating) {\n                    self.$elem.parent().find('.br-current-rating').text(text);\n                }\n            };\n\n            // return rounded fraction of a value (14.4 -> 40, 0.99 -> 90)\n            var fraction = function(value) {\n                return Math.round(((Math.floor(value * 10) / 10) % 1) * 100);\n            };\n\n            // remove all classes from elements\n            var resetStyle = function() {\n                // remove all classes starting with br-*\n                self.$widget.find('a').removeClass(function(index, classes) {\n                    return (classes.match(/(^|\\s)br-\\S+/g) || []).join(' ');\n                });\n            };\n\n            // apply style by setting classes on elements\n            var applyStyle = function() {\n                var $a = self.$widget.find('a[data-rating-value=\"' + ratingValue() + '\"]');\n                var initialRating = getData('userOptions').initialRating;\n                var baseValue = $.isNumeric(ratingValue()) ? ratingValue() : 0;\n                var f = fraction(initialRating);\n                var $all, $fractional;\n\n                resetStyle();\n\n                // add classes\n                $a.addClass('br-selected br-current')[nextAllorPreviousAll()]()\n                    .addClass('br-selected');\n\n                if (!getData('ratingMade') && $.isNumeric(initialRating)) {\n                    if ((initialRating <= baseValue) || !f) {\n                        return;\n                    }\n\n                    $all = self.$widget.find('a');\n\n                    $fractional = ($a.length) ?\n                        $a[(getData('userOptions').reverse) ? 'prev' : 'next']() :\n                        $all[(getData('userOptions').reverse) ? 'last' : 'first']();\n\n                    $fractional.addClass('br-fractional');\n                    $fractional.addClass('br-fractional-' + f);\n                }\n            };\n\n            // check if the element is deselectable?\n            var isDeselectable = function($element) {\n                if (!getData('allowEmpty') || !getData('userOptions').deselectable) {\n                    return false;\n                }\n\n                return (ratingValue() == $element.attr('data-rating-value'));\n            };\n\n            // handle click events\n            var attachClickHandler = function($elements) {\n                $elements.on('click.barrating', function(event) {\n                    var $a = $(this),\n                        options = getData('userOptions'),\n                        value,\n                        text;\n\n                    event.preventDefault();\n\n                    value = $a.attr('data-rating-value');\n                    text = $a.attr('data-rating-text');\n\n                    // is current and deselectable?\n                    if (isDeselectable($a)) {\n                        value = getData('emptyRatingValue');\n                        text = getData('emptyRatingText');\n                    }\n\n                    // remember selected rating\n                    setData('ratingValue', value);\n                    setData('ratingText', text);\n                    setData('ratingMade', true);\n\n                    setSelectFieldValue(value);\n                    showSelectedRating(text);\n\n                    applyStyle();\n\n                    // onSelect callback\n                    options.onSelect.call(\n                        self,\n                        ratingValue(),\n                        ratingText(),\n                        event\n                    );\n\n                    return false;\n                });\n            };\n\n            // handle mouseenter events\n            var attachMouseEnterHandler = function($elements) {\n                $elements.on('mouseenter.barrating', function() {\n                    var $a = $(this);\n\n                    resetStyle();\n\n                    $a.addClass('br-active')[nextAllorPreviousAll()]()\n                        .addClass('br-active');\n\n                    showSelectedRating($a.attr('data-rating-text'));\n                });\n            };\n\n            // handle mouseleave events\n            var attachMouseLeaveHandler = function($elements) {\n                self.$widget.on('mouseleave.barrating blur.barrating', function() {\n                    showSelectedRating();\n                    applyStyle();\n                });\n            };\n\n            // somewhat primitive way to remove 300ms click delay on touch devices\n            // for a more advanced solution consider setting `fastClicks` option to false\n            // and using a library such as fastclick (https://github.com/ftlabs/fastclick)\n            var fastClicks = function($elements) {\n                $elements.on('touchstart.barrating', function(event) {\n                    event.preventDefault();\n                    event.stopPropagation();\n\n                    $(this).click();\n                });\n            };\n\n            // disable clicks\n            var disableClicks = function($elements) {\n                $elements.on('click.barrating', function(event) {\n                    event.preventDefault();\n                });\n            };\n\n            var attachHandlers = function($elements) {\n                // attach click event handler\n                attachClickHandler($elements);\n\n                if (self.options.hoverState) {\n                    // attach mouseenter event handler\n                    attachMouseEnterHandler($elements);\n\n                    // attach mouseleave event handler\n                    attachMouseLeaveHandler($elements);\n                }\n            };\n\n            var detachHandlers = function($elements) {\n                // remove event handlers in the \".barrating\" namespace\n                $elements.off('.barrating');\n            };\n\n            var setupHandlers = function(readonly) {\n                var $elements = self.$widget.find('a');\n\n                if (fastClicks) {\n                    fastClicks($elements);\n                }\n\n                if (readonly) {\n                    detachHandlers($elements);\n                    disableClicks($elements);\n                } else {\n                    attachHandlers($elements);\n                }\n            };\n\n            this.show = function() {\n                // run only once\n                if (getData()) return;\n\n                // wrap element\n                wrapElement();\n\n                // save data\n                saveDataOnElement();\n\n                // build & append widget to the DOM\n                self.$widget = buildWidget();\n                self.$widget.insertAfter(self.$elem);\n\n                applyStyle();\n\n                showSelectedRating();\n\n                setupHandlers(self.options.readonly);\n\n                // hide the select field\n                self.$elem.hide();\n            };\n\n            this.readonly = function(state) {\n                if (typeof state !== 'boolean' || getData('readOnly') == state) return;\n\n                setupHandlers(state);\n                setData('readOnly', state);\n                self.$widget.toggleClass('br-readonly');\n            };\n\n            this.set = function(value) {\n                var options = getData('userOptions');\n\n                if (self.$elem.find('option[value=\"' + value + '\"]').length === 0) return;\n\n                // set data\n                setData('ratingValue', value);\n                setData('ratingText', self.$elem.find('option[value=\"' + value + '\"]').text());\n                setData('ratingMade', true);\n\n                setSelectFieldValue(ratingValue());\n                showSelectedRating(ratingText());\n\n                applyStyle();\n\n                // onSelect callback\n                if (!options.silent) {\n                    options.onSelect.call(\n                        this,\n                        ratingValue(),\n                        ratingText()\n                    );\n                }\n            };\n\n            this.clear = function() {\n                var options = getData('userOptions');\n\n                // restore original data\n                setData('ratingValue', getData('originalRatingValue'));\n                setData('ratingText', getData('originalRatingText'));\n                setData('ratingMade', false);\n\n                resetSelectField();\n                showSelectedRating(ratingText());\n\n                applyStyle();\n\n                // onClear callback\n                options.onClear.call(\n                    this,\n                    ratingValue(),\n                    ratingText()\n                );\n            };\n\n            this.destroy = function() {\n                var value = ratingValue();\n                var text = ratingText();\n                var options = getData('userOptions');\n\n                // detach handlers\n                detachHandlers(self.$widget.find('a'));\n\n                // remove widget\n                self.$widget.remove();\n\n                // remove data\n                removeDataOnElement();\n\n                // unwrap the element\n                unwrapElement();\n\n                // show the element\n                self.$elem.show();\n\n                // onDestroy callback\n                options.onDestroy.call(\n                    this,\n                    value,\n                    text\n                );\n            };\n        }\n\n        BarRating.prototype.init = function (options, elem) {\n            this.$elem = $(elem);\n            this.options = $.extend({}, $.fn.barrating.defaults, options);\n\n            return this.options;\n        };\n\n        return BarRating;\n    })();\n\n    $.fn.barrating = function (method, options) {\n        return this.each(function () {\n            var plugin = new BarRating();\n\n            // plugin works with select fields\n            if (!$(this).is('select')) {\n                $.error('Sorry, this plugin only works with select fields.');\n            }\n\n            // method supplied\n            if (plugin.hasOwnProperty(method)) {\n                plugin.init(options, this);\n                if (method === 'show') {\n                    return plugin.show(options);\n                } else {\n                    // plugin exists?\n                    if (plugin.$elem.data('barrating')) {\n                        plugin.$widget = $(this).next('.br-widget');\n                        return plugin[method](options);\n                    }\n                }\n\n            // no method supplied or only options supplied\n            } else if (typeof method === 'object' || !method) {\n                options = method;\n                plugin.init(options, this);\n                return plugin.show();\n\n            } else {\n                $.error('Method ' + method + ' does not exist on jQuery.barrating');\n            }\n        });\n    };\n\n    $.fn.barrating.defaults = {\n        theme:'',\n        initialRating:null, // initial rating\n        allowEmpty:null, // allow empty ratings?\n        emptyValue:'', // this is the expected value of the empty rating\n        showValues:false, // display rating values on the bars?\n        showSelectedRating:true, // append a div with a rating to the widget?\n        deselectable:true, // allow to deselect ratings?\n        reverse:false, // reverse the rating?\n        readonly:false, // make the rating ready-only?\n        fastClicks:true, // remove 300ms click delay on touch devices?\n        hoverState:true, // change state on hover?\n        silent:false, // supress callbacks when controlling ratings programatically\n        onSelect:function (value, text, event) {\n        }, // callback fired when a rating is selected\n        onClear:function (value, text) {\n        }, // callback fired when a rating is cleared\n        onDestroy:function (value, text) {\n        } // callback fired when a widget is destroyed\n    };\n\n    $.fn.barrating.BarRating = BarRating;\n\n}));\n"], "sourceRoot": "/source/"}